package top.mybi.modules.edge.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-07-27
 */
@Data
@ApiModel(value = "")
public class TCameraInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID")
	private Long id;

	@ApiModelProperty(value = "创建时间")
	private Date twfCreated;

	@ApiModelProperty(value = "更新时间")
	private Date twfModified;

	@ApiModelProperty(value = "删除时间")
	private Date twfDeleted;

	@ApiModelProperty(value = "地址")
	private String addr;

	@ApiModelProperty(value = "组ID")
	@JsonProperty("gId")
	private String gId;

	@ApiModelProperty(value = "分组名称")
	@JsonProperty("gName")
	private String gName;

	@ApiModelProperty(value = "FUUID")
	private String fUuid;

	@ApiModelProperty(value = "摄像头IP地址")
	private String ipAddr;

	@ApiModelProperty(value = "纬度")
	private String lat;

	@ApiModelProperty(value = "经度")
	private String lng;

	@ApiModelProperty(value = "名称")
	private String name;

	@ApiModelProperty(value = "项目")
	private String project;

	@ApiModelProperty(value = "小组")
	private String subgroup;

	@ApiModelProperty(value = "类型")
	private String type;

	@ApiModelProperty(value = "UUID")
	private String uuid;

	@ApiModelProperty(value = "摄像头ID")
	private String camId;

	@ApiModelProperty(value = "配置")
	private String config;

	@ApiModelProperty(value = "调试模式")
	private String debugmode;

	@ApiModelProperty(value = "能量")
	private String energy;

	@ApiModelProperty(value = "摄像头制造商")
	private String manufacturer;

	@ApiModelProperty(value = "onvif端口")
	private String onvifPort;

	@ApiModelProperty(value = "图片")
	private String pic;

	@ApiModelProperty(value = "协议ID")
	private String protocolId;

	@ApiModelProperty(value = "道路方向")
	private String roadDirection;

	@ApiModelProperty(value = "用户密码")
	private String streamPassword;

	@ApiModelProperty(value = "视频流地址1")
	private String streamUrl;

	@ApiModelProperty(value = "视频流地址2")
	private String streamUrl2;

	@ApiModelProperty(value = "用户名")
	private String streamUser;

	@ApiModelProperty(value = "摄像头在线状态 0-离线 1-在线 2-检测中")
	private Integer onlineStatus;

	@ApiModelProperty(value = "最后检测时间")
	private Date lastCheckTime;

	@ApiModelProperty(value = "摄像头类型 1-海康导入 2-地址访问")
	private Integer cameraType;

}