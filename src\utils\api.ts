import {http} from '@/utils/http'

// 安全API调用包装函数
export async function safeApiCall<T>(apiFunction: () => Promise<T>): Promise<T | {code: number, data: null, msg: string}> {
    try {
        return await apiFunction();
    } catch (error) {
        console.error('API调用错误: ', error);
        return { code: -1, data: null, msg: '请求失败' };
    }
}

export function home(){
    return http({
        url:'/edge/common/totalDevice',
        method:'get'
    })
}

// 获取系统信息列表
// 返回 Dashboard.BaseInfo
export function os(){
    return http({
        url:'/edge/common/os',
        method:'get'
    })
}

// 获取Dashboard信息
// 返回 Dashboard.CurrentInfo
export function current(){
    return http({
        url:'/edge/common/current',
        method:'get'
    })
}

// 模型视频等使用信息
// 返回 Dashboard.ModelInfoDto
export function getModelInfo(){
    return http({
        url:'/edge/common/getModelInfo',
        method:'get'
    })
}

// 获取GPU负载
export function getGpuLoad(){
    return http({
        url:'/edge/common/getGpuLoad',
        method:'get'
    })
}

// 获取模型分类
export function getModelCategory(){
    return http({
        url:'/edge/tmodelcategory/AllInfo',
        method:'get'
    })
}

// 获取当日告警统计（按级别）
export function getTodayAlarmCountByLevel(){
    return http({
        url:'/edge/common/todayAlarmCountByLevel',
        method:'get'
    })
}

// 获取所有告警统计（按级别）
export function getAllAlarmCountByLevel(){
    return http({
        url:'/edge/common/alarmCountByLevel',
        method:'get'
    })
}

// 摄像头分组表
export function getCameraGrouping(){
    return http({
        url:'edge/cameragrouping/selectAllGroups',
        method:'get'
    })
}

// 摄像头分组表tree
export function getCameraGroupingTree(){
    return http({
        url:'edge/cameragrouping/getTree',
        method:'get'
    })
}

// 更新摄像头分组
export function updateCameraGrouping(data:any){
    return http({
        url:'/edge/tcamerainfo/updateGroupInfo',
        method:'put',
        data:data
    })
}

// 根据分组ID获取摄像头列表
export function getCameraInfoByGroupId(gId:number){
    return http({
        url:`/edge/tcamerainfo/getCameraInfoByGroupId/${gId}`,
        method:'get'
    })
}

// 分组控制
export function startGroupControl(data:any){
    return http({
        url:`/edge/predictdeploy/startGroupControl`,
        method:'post',
        data:data
    })
}

// 检测摄像头连通性
export function checkCameraConnectivity(ipAddr: string){
    return http({
        url:`/edge/tcamerainfo/checkConnectivity`,
        method:'post',
        data: { ipAddr }
    })
}

// 检测摄像头流状态
export function checkCameraStreamStatus(streamUrl: string){
    return http({
        url:`/edge/tcamerainfo/checkStreamStatus`,
        method:'post',
        data: { streamUrl }
    })
}

// 检测单个摄像头状态
export function checkSingleCameraStatus(id: number){
    return http({
        url:`/edge/tcamerainfo/checkSingleStatus/${id}`,
        method:'post'
    })
}

// 检测所有摄像头状态
export function checkAllCameraStatus(){
    return http({
        url:`/edge/tcamerainfo/checkAllStatus`,
        method:'post'
    })
}

// 获取摄像头状态统计
export function getCameraStatusStatistics(){
    return http({
        url:`/edge/tcamerainfo/getStatusStatistics`,
        method:'get'
    })
}
