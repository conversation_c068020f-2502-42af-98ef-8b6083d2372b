# 摄像头状态检测后端实现说明

## 功能概述

为摄像头管理系统实现了完整的状态检测功能，包括定时任务、API接口和状态统计。支持海康导入和地址访问两种类型的摄像头状态检测。

## 实现的功能

### 1. 数据库表结构扩展
- 添加 `online_status` 字段：摄像头在线状态（0-离线，1-在线，2-检测中）
- 添加 `last_check_time` 字段：最后检测时间
- 添加 `camera_type` 字段：摄像头类型（1-海康导入，2-地址访问）

### 2. 定时任务
- **每2小时自动检测**：使用Spring的@Scheduled注解实现
- **应用启动初始化**：启动后5分钟执行一次初始检测
- **并发检测**：使用线程池并发检测多个摄像头，提高效率

### 3. 状态检测逻辑
#### 海康导入摄像头检测：
1. IP连通性检测（ping + TCP端口检测）
2. 流地址可用性检测
3. 可扩展海康API状态查询

#### 地址访问摄像头检测：
1. IP连通性检测（优先）
2. RTSP流连接检测
3. TCP端口连通性检测（80, 554, 8000端口）

### 4. API接口
- `POST /edge/tcamerainfo/checkConnectivity` - 检测IP连通性
- `POST /edge/tcamerainfo/checkStreamStatus` - 检测流状态
- `POST /edge/tcamerainfo/checkSingleStatus/{id}` - 检测单个摄像头
- `POST /edge/tcamerainfo/checkAllStatus` - 检测所有摄像头
- `GET /edge/tcamerainfo/getStatusStatistics` - 获取状态统计

### 5. 状态统计
- 总数量统计
- 在线/离线/检测中数量统计
- 在线率计算
- 未检测数量统计

## 文件结构

```
public/java/
├── TCameraInfoEntity.java          # 实体类（已更新）
├── TCameraInfoDTO.java             # DTO类（已更新）
├── TCameraInfoController.java      # 控制器（已更新）
├── TCameraInfoService.java         # 服务接口（已更新）
├── TCameraInfoServiceImpl.java     # 服务实现（已更新）
├── CameraStatusService.java        # 状态检测服务接口（新增）
├── CameraStatusServiceImpl.java    # 状态检测服务实现（新增）
├── CameraStatusScheduler.java      # 定时任务（新增）
├── SchedulingConfig.java           # 定时任务配置（新增）
└── camera_status_update.sql        # 数据库更新脚本（新增）
```

## 部署步骤

### 1. 数据库更新
执行 `camera_status_update.sql` 脚本更新数据库表结构：
```sql
-- 添加状态相关字段
ALTER TABLE t_camera_info 
ADD COLUMN online_status INT(1) DEFAULT NULL COMMENT '摄像头在线状态 0-离线 1-在线 2-检测中',
ADD COLUMN last_check_time DATETIME DEFAULT NULL COMMENT '最后检测时间',
ADD COLUMN camera_type INT(1) DEFAULT 2 COMMENT '摄像头类型 1-海康导入 2-地址访问';
```

### 2. 代码部署
1. 将所有Java文件复制到对应的包路径下
2. 确保Spring Boot项目已启用定时任务功能
3. 重新编译和部署应用

### 3. 配置验证
- 检查定时任务是否正常启动
- 验证API接口是否可访问
- 确认数据库连接正常

## API使用示例

### 检测IP连通性
```bash
POST /edge/tcamerainfo/checkConnectivity
Content-Type: application/json

{
    "ipAddr": "*************"
}
```

### 检测流状态
```bash
POST /edge/tcamerainfo/checkStreamStatus
Content-Type: application/json

{
    "streamUrl": "rtsp://*************:554/stream1"
}
```

### 获取状态统计
```bash
GET /edge/tcamerainfo/getStatusStatistics
```

响应示例：
```json
{
    "code": 0,
    "data": {
        "total": 100,
        "online": 85,
        "offline": 10,
        "checking": 3,
        "uncheck": 2,
        "onlineRate": 85.0
    }
}
```

## 定时任务配置

### 修改检测频率
在 `CameraStatusScheduler.java` 中修改cron表达式：
```java
// 每2小时执行：0 0 */2 * * ?
// 每1小时执行：0 0 */1 * * ?
// 每30分钟执行：0 */30 * * * ?
@Scheduled(cron = "0 0 */2 * * ?")
```

### 调整超时时间
在 `CameraStatusServiceImpl.java` 中修改超时常量：
```java
private static final int CONNECTION_TIMEOUT = 5000; // TCP连接超时
private static final int PING_TIMEOUT = 3000;       // Ping超时
```

## 性能优化

1. **并发检测**：使用线程池并发检测多个摄像头
2. **连接复用**：TCP连接检测后立即关闭，避免资源占用
3. **超时控制**：设置合理的超时时间，避免长时间等待
4. **数据库索引**：为状态字段创建索引，提高查询性能

## 监控和日志

- 所有检测操作都有详细的日志记录
- 定时任务执行情况会记录耗时
- 检测失败的摄像头会记录警告日志
- 可通过日志监控系统整体运行状况

## 扩展建议

1. **海康API集成**：可进一步集成海康的设备状态查询API
2. **告警机制**：摄像头离线时发送告警通知
3. **状态历史**：记录摄像头状态变化历史
4. **批量操作**：支持批量启用/禁用状态检测
5. **自定义检测策略**：支持不同类型摄像头使用不同检测策略
