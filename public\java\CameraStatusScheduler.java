package top.mybi.modules.edge.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.mybi.modules.edge.service.CameraStatusService;

/**
 * 摄像头状态检测定时任务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class CameraStatusScheduler {
    
    @Autowired
    private CameraStatusService cameraStatusService;
    
    /**
     * 每2小时执行一次摄像头状态检测
     * cron表达式: 0 0 */2 * * ? 表示每2小时的整点执行
     */
    @Scheduled(cron = "0 0 */2 * * ?")
    public void scheduledCameraStatusCheck() {
        log.info("定时任务开始：检测所有摄像头状态");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行状态检测
            cameraStatusService.checkAllCameraStatus();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("定时任务完成：摄像头状态检测完成，耗时: {} ms", duration);
            
        } catch (Exception e) {
            log.error("定时任务执行失败：摄像头状态检测", e);
        }
    }
    
    /**
     * 应用启动后5分钟执行一次初始检测
     */
    @Scheduled(initialDelay = 5 * 60 * 1000, fixedDelay = Long.MAX_VALUE)
    public void initialCameraStatusCheck() {
        log.info("应用启动初始化：开始检测所有摄像头状态");
        
        try {
            cameraStatusService.checkAllCameraStatus();
            log.info("应用启动初始化：摄像头状态检测完成");
        } catch (Exception e) {
            log.error("应用启动初始化：摄像头状态检测失败", e);
        }
    }
}
