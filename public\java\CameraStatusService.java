package top.mybi.modules.edge.service;

import top.mybi.modules.edge.dto.TCameraInfoDTO;

import java.util.List;

/**
 * 摄像头状态检测服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CameraStatusService {
    
    /**
     * 检测单个摄像头状态
     * @param cameraInfo 摄像头信息
     * @return 在线状态 0-离线 1-在线 2-检测中
     */
    Integer checkSingleCameraStatus(TCameraInfoDTO cameraInfo);
    
    /**
     * 批量检测摄像头状态
     * @param cameraList 摄像头列表
     */
    void batchCheckCameraStatus(List<TCameraInfoDTO> cameraList);
    
    /**
     * 检测所有摄像头状态
     */
    void checkAllCameraStatus();
    
    /**
     * 检测海康摄像头状态
     * @param cameraInfo 摄像头信息
     * @return 在线状态
     */
    Integer checkHikvisionCameraStatus(TCameraInfoDTO cameraInfo);
    
    /**
     * 检测RTSP流摄像头状态
     * @param cameraInfo 摄像头信息
     * @return 在线状态
     */
    Integer checkRtspCameraStatus(TCameraInfoDTO cameraInfo);
    
    /**
     * 通过IP地址检测摄像头连通性
     * @param ipAddress IP地址
     * @return 是否连通
     */
    boolean checkCameraConnectivity(String ipAddress);
    
    /**
     * 检测RTSP流是否可用
     * @param streamUrl 流地址
     * @return 是否可用
     */
    boolean checkStreamAvailability(String streamUrl);
}
