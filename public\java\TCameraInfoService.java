package top.mybi.modules.edge.service;

import org.springframework.web.bind.annotation.RequestParam;
import top.mybi.common.service.CrudService;
import top.mybi.modules.edge.dto.CameraGroupUpdateDTO;
import top.mybi.modules.edge.dto.TCameraInfoDTO;
import top.mybi.modules.edge.entity.TCameraInfoEntity;
import java.util.List;
/**
 *
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-07-27
 */
public interface TCameraInfoService extends CrudService<TCameraInfoEntity, TCameraInfoDTO> {
    TCameraInfoDTO getCameraInfoByUUid(String uuid) throws Exception;

    void hikvisionSync(@RequestParam String host, @RequestParam String appKey, @RequestParam String appSecret) throws Exception;
    List<TCameraInfoDTO> getNameList(String name,String id);

    List<String> getUuidList(Long[] ids);

    /**
     * 根据设备名称查询
     * 用来获取当前摄像头是那家公司的
     * @param deviceName
     * @return
     */
    TCameraInfoEntity selectByName(String deviceName);

    /**
     * 根据租户ID查询摄像头名称列表
     * @param tenantId
     * @return
     */
    List<String> selectByTenantId(Integer tenantId);

    /**
     * 获取摄像头总数
     * @return
     */
    long getCameraCount();

    /**
     * 更新分组信息
     * @param dtos
     */
    void updateGroupInfo(List<CameraGroupUpdateDTO> dtos);

    /**
     * 根据分组ID获取摄像头信息
     * @param gId
     * @return
     */
    List<TCameraInfoDTO> getCameraInfoByGroupId(Long gId);

}
