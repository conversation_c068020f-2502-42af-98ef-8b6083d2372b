package top.mybi.modules.edge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-07-27
 */
@Data
@TableName("t_camera_info")
public class TCameraInfoEntity {

    /**
     * ID
     */
//    @TableId(value = "id",type = IdType.AUTO)
	private Long id;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
	private Date twfCreated;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date twfModified;
    /**
     * 删除时间
     */
	private Date twfDeleted;
    /**
     * 地址
     */
	private String addr;
    /**
     * 组ID
     */
    private String gId;
    /**
     * 摄像头分组名称
     */
    private String gName;
    /**
     * FUUID
     */
	private String fUuid;
    /**
     * 摄像头IP地址
     */
	private String ipAddr;
    /**
     * 纬度
     */
	private String lat;
    /**
     * 经度
     */
	private String lng;
    /**
     * 名称
     */
	private String name;
    /**
     * 项目
     */
	private String project;
    /**
     * 小组
     */
	private String subgroup;
    /**
     * 类型
     */
	private String type;
    /**
     * UUID
     */
	private String uuid;
    /**
     * 摄像头ID
     */
	private String camId;
    /**
     * 配置
     */
	private String config;
    /**
     * 调试模式
     */
	private String debugmode;
    /**
     * 能量
     */
	private String energy;
    /**
     * 摄像头制造商
     */
	private String manufacturer;
    /**
     * onvif端口
     */
	private String onvifPort;
    /**
     * 图片
     */
	private String pic;
    /**
     * 协议ID
     */
	private String protocolId;
    /**
     * 道路方向
     */
	private String roadDirection;
    /**
     * 用户密码
     */
	private String streamPassword;
    /**
     * 视频流地址1
     */
	private String streamUrl;
    /**
     * 视频流地址2
     */
	private String streamUrl2;
    /**
     * 用户名
     */
	private String streamUser;

    /**
     * 摄像头在线状态 0-离线 1-在线 2-检测中
     */
    private Integer onlineStatus;

    /**
     * 最后检测时间
     */
    private Date lastCheckTime;

    /**
     * 摄像头类型 1-海康导入 2-地址访问
     */
    private Integer cameraType;
}