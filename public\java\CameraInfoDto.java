package top.mybi.modules.edge.dto;

import lombok.Data;

@Data
public class CameraInfoDto {

    private String cameraIndexCode;     // 监控点编号（UUID）
    private String gbIndexCode;         // 国标编号
    private String name;                // 监控点名称
    private String deviceIndexCode;     // 所属设备编号（UUID）
    private String longitude;           // 经度（WGS84）
    private String latitude;            // 纬度（WGS84）
    private String altitude;            // 海拔（单位：米）
    private Integer pixel;              // 摄像机像素（数据字典xresmgr.piexl）
    private Integer cameraType;         // 监控点类型（数据字典xresmgr.camera_type）
    private String cameraTypeName;      // 监控点类型说明
    private String installPlace;        // 安装位置
    private String matrixCode;          // 矩阵编号
    private Integer chanNum;            // 通道号
//    private String viewshed;            // 可视域（JSON格式）
//    private String capabilitySet;       // 能力集（数据字典xresmgr.capability_set）
//    private String capabilitySetName;   // 能力集说明
//    private String intelligentSet;      // 智能分析能力集（数据字典xresmgr.intelligent_set）
//    private String intelligentSetName;  // 智能分析能力集说明
//    private String recordLocation;      // 录像存储位置（0-中心，1-设备）
//    private String recordLocationName;  // 存储位置说明
//    private Integer ptzController;      // 云台控制类型（数据字典xresmgr.ptz_control_type）
//    private String ptzControllerName;   // 云台控制说明
//    private String deviceResourceType;  // 所属设备类型（数据字典xresmgr.resource_type）
//    private String deviceResourceTypeName; // 所属设备类型说明
//    private String channelType;         // 通道子类型（数据字典xresmgr.device_type_code.camera）
//    private String channelTypeName;     // 通道子类型说明
    private Integer transType;          // 传输协议（0-UDP，1-TCP）
    private String transTypeName;       // 传输协议说明
    private String updateTime;          // 更新时间（ISO8601）
    private String unitIndexCode;       // 所属组织编号（UUID）
    private String treatyType;          // 接入协议（数据字典xresmgr.protocol_type）
    private String treatyTypeName;      // 协议类型说明
    private String createTime;          // 创建时间（ISO8601）
    private Integer status;             // 在线状态（0-不在线，1-在线）
    private String statusName;          // 状态说明

}

