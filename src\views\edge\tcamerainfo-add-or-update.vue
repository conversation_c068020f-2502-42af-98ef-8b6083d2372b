<template>
  <el-dialog align-center v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <div style="margin-bottom: 20px; text-align: center;">
      <el-radio-group v-model="activeTab">
        <el-radio-button label="Manuel">手动添加</el-radio-button>
        <el-radio-button label="Hikvi" v-if="!dataForm.id">海康导入</el-radio-button>
      </el-radio-group>
    </div>

    <div v-if="activeTab === 'Manuel'">
      <el-form label-position="left" :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
        <el-form-item label="UUID" prop="uuid">
          <el-input v-model="dataForm.uuid" placeholder="UUID"></el-input>
        </el-form-item>

        <el-form-item :label="$t('edge.name')" prop="name">
          <el-input v-model="dataForm.name" :placeholder="$t('edge.name')"></el-input>
        </el-form-item>

        <!-- <el-form-item label="组ID" prop="gId">
          <el-input v-model="dataForm.gId" placeholder="组ID"></el-input>
        </el-form-item> -->
        <el-form-item label="配置分组" prop="gId">
          <el-select v-model="dataForm.gId" placeholder="请选择分组" clearable filterable @change="handleGroupChange">
            <el-option
              v-for="group in groupList"
              :key="group.id"
              :label="group.gName"
              :value="group.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('edge.type')" prop="type">
          <el-input v-model="dataForm.type" :placeholder="$t('edge.type')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('edge.cameraID')" prop="camId">
          <el-input v-model="dataForm.camId" :placeholder="$t('edge.cameraID')"></el-input>
        </el-form-item>

        <el-form-item label="所属公司" v-model="dataForm.tenantId" v-if="isShow">
          <el-select filterable ref="itemSelect" v-model="dataForm.tenantId" placeholder="选择公司">
            <el-option v-for="item in deptName as any" :key="item.id" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('edge.cameraManufacturer')">
          <el-input v-model="dataForm.manufacturer" :placeholder="$t('edge.cameraManufacturer')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('edge.cameraIPAddress')" prop="ipAddr">
          <el-input v-model="dataForm.ipAddr" :placeholder="$t('edge.cameraIPAddress')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('edge.videoStreamAddress1')" prop="streamUrl">
          <el-input v-model="dataForm.streamUrl" :placeholder="$t('edge.videoStreamAddress1')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('edge.username')" prop="streamUser">
          <el-input v-model="dataForm.streamUser" :placeholder="$t('edge.username')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('edge.password')" prop="streamPassword">
          <el-input v-model="dataForm.streamPassword" :placeholder="$t('edge.password')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('edge.longitude')" prop="lng">
          <el-input v-model="dataForm.lng" :placeholder="$t('edge.longitude')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('edge.latitude')" prop="lat">
          <el-input v-model="dataForm.lat" :placeholder="$t('edge.latitude')"></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="activeTab === 'Hikvi' && !dataForm.id">
      <el-form label-position="left" :model="dataFormHiKvi" ref="dataFormHiKviRef" @keyup.enter.native="dataFormSubmitHandle()" label-width="auto">
        <el-form-item label="host" prop="host">
          <el-input v-model="dataFormHiKvi.host" placeholder="host ip:端口"></el-input>
        </el-form-item>
        <el-form-item label="appKey" prop="appKey">
          <el-input v-model="dataFormHiKvi.appKey" placeholder="appKey"></el-input>
        </el-form-item>
        <el-form-item label="appSecret" prop="appSecret">
          <el-input v-model="dataFormHiKvi.appSecret" placeholder="appSecret"></el-input>
        </el-form-item>
      </el-form>
    </div>

    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button color="rgba(50,122,230,1)" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { globalLanguage } from "@/utils/globaLang";
import initDataForm from "@/utils/initDataForm";
import { getCameraGrouping } from "@/utils/api";

const { $t } = globalLanguage();
const emit = defineEmits(["refreshDataList"]);
const activeTab = ref("Manuel");
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: "",
  uuid: "",
  name: "",
  type: 0,
  lng: "",
  lat: "",
  camId: "",
  manufacturer: "",
  ipAddr: "",
  streamUrl: "",
  streamUser: "",
  streamPassword: "",
  tenantId: "",
  gId: "",
  gName: ""
});
const initForm = initDataForm(dataForm);

interface CameraGroupItem {
  id: string;
  gName: string;
}
const groupList = ref<CameraGroupItem[]>([]);

const dataFormHiKviRef = ref();
const dataFormHiKvi = reactive({
  host: "********:443", //  http://********:9017, https://********:443
  appKey: "20743784",
  appSecret: "SUmtyYODlwx0jENwGFTn"
});
const initFormHiKvi = initDataForm(dataFormHiKvi);
const rules = ref({
  ipAddr: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  name: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  type: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  uuid: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  camId: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ],
  streamUrl: [
    {
      required: true,
      message: $t("validate.required"),
      trigger: "blur"
    }
  ]
});
const a = ref(0);
const init = (id?: string, status?: number) => {
  visible.value = true;
  dataForm.id = "";
  a.value = status || 0;
  activeTab.value = "Manuel";

  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    Object.assign(dataForm, initForm);
  }
  if (dataFormHiKviRef.value) {
    dataFormHiKviRef.value.resetFields();
    Object.assign(dataFormHiKvi, initFormHiKvi);
  }
  loadGroupList();

  if (id) {
    getInfo(id);
  }
};

const loadGroupList = async () => {
  try {
    const response = await getCameraGrouping();
    let actualData: CameraGroupItem[] = [];
    if (response && typeof response.data !== 'undefined' && typeof (response as any).code === 'undefined') {
        if (response.data && response.data.code === 0 && Array.isArray(response.data.data)) {
            actualData = response.data.data;
        } else {
            console.error("加载分组列表失败 (AxiosResponse.data 格式错误):", response.data);
        }
    } else if (response && typeof (response as any).code === 'number') {
        if ((response as any).code === 0 && Array.isArray((response as any).data)) {
            actualData = (response as any).data;
        } else {
            console.error("加载分组列表失败 (直接响应格式错误):", response);
        }
    } else if (Array.isArray(response)){
        actualData = response;
    } else {
        console.error("加载分组列表失败: 未知响应格式", response);
    }
    groupList.value = actualData;
  } catch (error) {
    console.error("加载摄像头分组列表时出错:", error);
    ElMessage.error("加载摄像头分组列表失败。");
    groupList.value = [];
  }
};

const handleGroupChange = (selectedGId: string) => {
  const selectedGroup = groupList.value.find(group => group.id === selectedGId);
  if (selectedGroup) {
    dataForm.gName = selectedGroup.gName;
  } else {
    dataForm.gName = "";
  }
};

const getInfo = (id: string) => {
  baseService.get("/edge/tcamerainfo/" + id).then((res: any) => {
    Object.assign(dataForm, res.data);
    activeTab.value = "Manuel";
  });
};

const dataFormSubmitHandle = () => {
  visible.value = false;
  if (activeTab.value === "Hikvi") {
    baseService.get("/edge/tcamerainfo/hikvisionSync", dataFormHiKvi).then(() => {
      ElMessage.success({
        message: $t("已添加海康平台导入任务"),
        duration: 1000,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  } else if (activeTab.value === "Manuel") {
    dataFormRef.value.validate((valid: boolean) => {
      if (!valid) {
        return false;
      }
      if (a.value == 1) {
        dataForm.id = "";
        baseService.post("/edge/tcamerainfo/", dataForm).then(() => {
          ElMessage.success({
            message: $t("手动导入" + "prompt.success"),
            duration: 500,
            onClose: () => {
              visible.value = false;
              emit("refreshDataList");
            }
          });
        });
      } else {
        (!dataForm.id ? baseService.post : baseService.put)("/edge/tcamerainfo/", dataForm).then(() => {
          ElMessage.success({
            message: $t("手动导入" + "prompt.success"),
            duration: 500,
            onClose: () => {
              visible.value = false;
              emit("refreshDataList");
            }
          });
        });
      }
    });
  }
};
const isShow = ref(true);
const deptName = ref([]);
baseService.get("/sys/tenant/list").then((res) => {
  deptName.value = res.data;

  if (deptName.value.length === 1) {
      isShow.value = false;
  }
});
defineExpose({
  init
});
</script>
