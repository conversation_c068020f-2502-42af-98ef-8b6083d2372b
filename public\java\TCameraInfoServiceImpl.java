package top.mybi.modules.edge.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.mybi.common.service.impl.CrudServiceImpl;
import top.mybi.common.utils.ConvertUtils;
import top.mybi.modules.edge.dao.TCameraInfoDao;
import top.mybi.modules.edge.dao.TuuidCameraDao;
import top.mybi.modules.edge.dto.CameraGroupUpdateDTO;
import top.mybi.modules.edge.dto.CameraInfoDto;
import top.mybi.modules.edge.dto.TCameraInfoDTO;
import top.mybi.modules.edge.entity.TCameraInfoEntity;
import top.mybi.modules.edge.service.TCameraInfoService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-07-27
 */
@Slf4j
@Service
public class TCameraInfoServiceImpl extends CrudServiceImpl<TCameraInfoDao, TCameraInfoEntity, TCameraInfoDTO> implements TCameraInfoService {
    @Autowired
    private TCameraInfoDao tCameraInfoDao;

    @Autowired
    private TuuidCameraDao tuuidCameraDao;

    @Override
    public QueryWrapper<TCameraInfoEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        String name = (String) params.get("name");
        String tenantId = (String) params.get("tenantId");
        String gId = (String) params.get("gId");

        QueryWrapper<TCameraInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        wrapper.like(StringUtils.isNotBlank(name), "name", name);
        wrapper.eq(StringUtils.isNotBlank(tenantId), "tenant_id", tenantId);
        wrapper.eq(StringUtils.isNotBlank(gId), "g_id", gId);
        return wrapper;
    }

    /**
     * * 根据UUID获取摄像头信息。
     * 此方法通常作为数据访问层与业务逻辑层之间的接口。
     *
     * @param uuid 摄像头的唯一标识符。
     * @return 包含摄像头信息的DTO对象。
     * @throws Exception 如果无法获取摄像头信息时抛出异常。
     */
    @Override
    public TCameraInfoDTO getCameraInfoByUUid(String uuid) throws Exception {
        // 验证参数
        if (uuid == null || uuid.isEmpty()) {
            throw new IllegalArgumentException("UUID cannot be null or empty.");
        }

        TCameraInfoEntity dto = this.baseDao.getCameraInfoByUUid(uuid);
        // 验证是否找到实体对象
        if (dto == null) {
            throw new Exception("No camera info found for UUID: " + uuid);
        }

        // 转换实体对象为DTO对象
        TCameraInfoDTO convertedDto = ConvertUtils.sourceToTarget(dto, TCameraInfoDTO.class);
        return convertedDto;
    }

    @Override
    public List<TCameraInfoDTO> getNameList(String name, String id) {
        List<TCameraInfoDTO> nameList = tCameraInfoDao.getNameList(name, id);
        return nameList;
    }

    @Override
    public List<String> getUuidList(Long[] ids) {
        return tCameraInfoDao.getUuidList(ids);
    }

    @Override
    public TCameraInfoEntity selectByName(String deviceName) {
        return tCameraInfoDao.getByDeviceName(deviceName);
    }

    @Override
    public List<String> selectByTenantId(Integer tenantId) {
        return tCameraInfoDao.getNameByTenantId(tenantId);
    }

    @Override
    public long getCameraCount() {
        // 获取摄像头总数
        QueryWrapper<TCameraInfoEntity> wrapper = new QueryWrapper<>();

        long count = tCameraInfoDao.selectCount(wrapper);
        return count;
    }

    @Override
    public void updateGroupInfo(List<CameraGroupUpdateDTO> dtos) {
        // 批量更新摄像���分组信息
        for (CameraGroupUpdateDTO dto : dtos) {
            // 更新摄像头分组信息
            tCameraInfoDao.updateGroupInfo(dto.getId(), dto.getGId(), dto.getGName());
        }
    }

    @Override
    public List<TCameraInfoDTO> getCameraInfoByGroupId(Long id) {
        return tCameraInfoDao.getCameraInfoByGroupId(id);
    }

    @Override
    public void hikvisionSync(String host, String appKey, String appSecret) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host); // 代理API网关nginx服务器ip端口
        config.setAppKey(appKey);  // 秘钥appkey
        config.setAppSecret(appSecret);// 秘钥appSecret
        String ARTEMIS_PATH = "/artemis";

        /*单独请求一次获取摄像头总数量*/
        final String getCamsApi = ARTEMIS_PATH + "/api/resource/v1/cameras"; //获取摄像头列表
        Map<String, String> camsParamMap = new HashMap<>();// post请求Form表单参数
        camsParamMap.put("pageNo", "1");
        camsParamMap.put("pageSize", "1");
        String countBody = JSON.toJSON(camsParamMap).toString();
        Map<String, String> path = new HashMap<>(2) {{
            put("https://", getCamsApi);
        }};
        String countRes = ArtemisHttpUtil.doPostStringArtemis(config, path, countBody, null, null, "application/json");
        JSONObject countJsonObject = JSON.parseObject(countRes);
        int total = countJsonObject.getJSONObject("data").getIntValue("total");

        /*获取全部摄像头列表*/
        camsParamMap.put("pageSize", String.valueOf(total));
        String camsBody = JSON.toJSON(camsParamMap).toString();
        String camsRes = ArtemisHttpUtil.doPostStringArtemis(config, path, camsBody, null, null, "application/json");
        JSONObject camJsonObject = JSON.parseObject(camsRes);
        List<CameraInfoDto> camInfoList = camJsonObject.getJSONObject("data").getJSONArray("list").toJavaList(CameraInfoDto.class);

        for (CameraInfoDto cam : camInfoList) {
            /*根据摄像头列表获取每个摄像头的视频流地址*/
            final String getStreamApi = ARTEMIS_PATH + "/api/vnsc/mls/v1/preview/openApi/getPreviewParam"; //获取摄像头列表
            Map<String, Object> streamParamMap = new HashMap<>();// post请求Form表单参数

            streamParamMap.put("indexCode", cam.getCameraIndexCode());
            streamParamMap.put("streamType", 0);
            streamParamMap.put("protocol", "rtsp");
            streamParamMap.put("transmode", 1);
            streamParamMap.put("expireTime", -1);
            String streamBody = JSON.toJSON(streamParamMap).toString();

            Map<String, String> streamPath = new HashMap<>(2) {
                {
                    put("https://", getStreamApi);
                }
            };
            String streamRes = ArtemisHttpUtil.doPostStringArtemis(config, streamPath, streamBody, null, null, "application/json");
            JSONObject streamJsonObject = JSON.parseObject(streamRes);
            // 读取总数字段
            String streamUrl = "";
            try {
                streamUrl = streamJsonObject.getJSONObject("data").getString("url");
            } catch (Exception e) {
                log.warn("未获取到摄像头流地址，cameraIndexCode: {}，返回内容: {}", cam.getCameraIndexCode(), streamRes);
                continue;
            }

            if (StringUtils.isEmpty(streamUrl)) {
                log.warn("未获取到摄像头流地址，cameraIndexCode: {}，返回内容: {}", cam.getCameraIndexCode(), streamRes);
                continue;
            }
            log.info("海康平台导入摄像头: " + cam.getName() + ", id: " + cam.getCameraIndexCode() + " =, url is: " + streamUrl);

            String timeStamp = String.valueOf(System.currentTimeMillis());
            TCameraInfoEntity entity = new TCameraInfoEntity();
            entity.setName(cam.getName());
            entity.setUuid(timeStamp);
            entity.setCamId(cam.getCameraIndexCode());
            entity.setStreamUrl(streamUrl);
            entity.setLat(cam.getLatitude());
            entity.setLng(cam.getLongitude());
            entity.setManufacturer("海康威视");
            entity.setType(cam.getCameraTypeName());
            entity.setAddr(cam.getInstallPlace());

            //如果摄像头已存在，就不再插入
            QueryWrapper<TCameraInfoEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("cam_id", cam.getCameraIndexCode());
            TCameraInfoEntity cameraInfoEntity = tCameraInfoDao.selectOne(wrapper);
            if (cameraInfoEntity != null) {
                update(entity, wrapper);
            } else {
                insert(entity);
            }
        }

        log.info("海康平台摄像头导入完成，共导入摄像头数量: {}", camInfoList.size());
    }

}

