<template>
  <!-- 摄像头管理 -->
  <div class="mod-bga__monitoringstation">
    <!-- 状态统计 -->
    <div class="camera-status-summary" style="margin-bottom: 16px; padding: 12px; background: #f5f7fa; border-radius: 4px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="status-item">
            <el-icon color="#67c23a" size="20"><VideoCameraFilled /></el-icon>
            <span style="margin-left: 8px; color: #67c23a; font-weight: bold;">
              在线: {{ onlineCameraCount }}
            </span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <el-icon color="#f56c6c" size="20"><Warning /></el-icon>
            <span style="margin-left: 8px; color: #f56c6c; font-weight: bold;">
              离线: {{ offlineCameraCount }}
            </span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <el-icon color="#409eff" size="20"><VideoCamera /></el-icon>
            <span style="margin-left: 8px; color: #409eff; font-weight: bold;">
              检测中: {{ checkingCameraCount }}
            </span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <el-icon color="#909399" size="20"><VideoCamera /></el-icon>
            <span style="margin-left: 8px; color: #909399; font-weight: bold;">
              总计: {{ totalCameraCount }}
            </span>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList">
      <el-form-item>
        <el-input v-model="state.dataForm.id" style="width: 200px" placeholder="id" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.name" style="width: 200px" placeholder="名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="state.dataForm.gId" placeholder="请选择分组" clearable filterable style="width: 200px;">
          <el-option
            v-for="group in searchGroupList"
            :key="group.id"
            :label="group.gName"
            :value="group.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-model="state.dataForm.tenantId" v-if="!isShow">
        <el-select filterable ref="itemSelect" v-model="state.dataForm.tenantId" placeholder="选择公司" @change="console.log(state.dataForm.tenantId)">
          <el-option v-for="item in deptName" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button :icon="Search" @click="state.getDataList"></el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:save')" color="rgba(50,122,230,1)" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('sys:user:export')" type="success" @click="toDeployHandle()">一键部署</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" style="width: 100%; z-index: 1" :data="filteredDataList" border show-overflow-tooltip @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="left" align="center" width="60"></el-table-column>
      <!-- <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="name" :label="$t('edge.name')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="gName" label="分组名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="ipAddr" :label="$t('edge.cameraIPAddress')" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="type" :label="$t('edge.type')" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="manufacturer" :label="$t('edge.cameraManufacturer')" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="streamUrl" :label="$t('edge.videoStreamAddress1')" header-align="center" align="center"></el-table-column> -->
      <el-table-column label="摄像头状态" header-align="center" align="center" width="150">
        <template #default="scope">
          <div class="camera-status-container">
            <el-tag
              :type="getCameraStatusType(scope.row)"
              :icon="getCameraStatusIcon(scope.row)"
              size="small"
              class="camera-status-tag"
              effect="dark"
            >
              {{ getCameraStatusText(scope.row) }}
            </el-tag>
            <span
              v-if="scope.row.lastCheckTime"
              class="camera-status-time"
              :class="{
                'status-online': scope.row.onlineStatus === 1,
                'status-offline': scope.row.onlineStatus === 0,
                'status-checking': scope.row.onlineStatus === 2
              }"
            >
              {{ formatLastCheckTime(scope.row.lastCheckTime) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="查看详情" header-align="center" align="center" width="150px">
        <template #default="scope">
          <el-button type="primary" plain size="small" v-if="state.hasPermission('sys:user:delete')" @click="details(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column label="查看运行算法" header-align="center" align="center" width="150px">
        <template #default="scope">
          <el-button type="primary" plain size="small" v-if="state.hasPermission('sys:user:delete')" @click="getTrainName(scope.row.streamUrl)">查看运行算法</el-button>
        </template>
      </el-table-column>

      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" :show-overflow-tooltip="false" width="380">
        <template #default="scope">
          <el-button type="info" size="small" @click="router.push({ path: '/edge/tcontroladdinfo', query: { streamUrl: scope.row.streamUrl, tid: scope.row.id } })">部署</el-button>
          <el-text v-if="scope.row.isStreaming" style="display: inline-block; vertical-align: middle">推流中...</el-text>
          <el-button type="success" link v-if="!scope.row.isStreaming" @click="startStreaming(scope.row)">开启推流</el-button>
          <el-button type="danger" link v-if="scope.row.isStreaming" @click="stopStreaming(scope.row)">停止推流</el-button>
          <el-button type="warning" link @click="openPlayer(scope.row.streamUrl, scope.row.id)">{{ $t("play") }}</el-button>
          <el-button type="warning" v-if="state.hasPermission('sys:user:update')" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <!-- //多加一个复制按钮 -->
          <el-button type="info" link @click="copyStreamUrl(scope.row.id)">复制</el-button>

          <el-button type="danger" link v-if="state.hasPermission('sys:user:delete')" @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          <!-- <el-button type="info" link @click="getTrainName(scope.row.streamUrl)">查看运行算法</el-button> -->
          <el-dropdown trigger="click" v-if="scope.row.isStreaming" style="display: inline-block; vertical-align: middle">
            <el-button v-if="scope.row.isStreaming" link icon="InfoFilled" @click="handleClickPushInfo(scope.row)">推流信息 </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>{{ pushArrList.flv_http }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
  <devicePlayer ref="devicePlayerRef"></devicePlayer>
  <el-dialog v-model="dialogTableVisible" title="摄像头详情" width="800" style="height: 400px; overflow: auto">
    <table border="1" cellspacing="0" bordercolor="#ccc" width="100%" style="text-align: center" v-for="item in gridData" :key="item.id">
      <tbody>
        <tr>
          <td style="color: #327ae6; font-weight: bold">名称</td>
          <td>{{ item.name }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">UUID</td>
          <td>{{ item.uuid }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">IP地址</td>
          <td>{{ item.ipAddr }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">类型</td>
          <td>{{ item.type }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">制造商</td>
          <td>{{ item.manufacturer }}</td>
        </tr>
        <tr>
          <td style="color: #327ae6; font-weight: bold">视频流地址</td>
          <td>{{ item.streamUrl }}</td>
        </tr>
      </tbody>
    </table>

    <br />
  </el-dialog>
</template>

<script lang="ts" setup>
import devicePlayer from "@/views/device/devicePlayer.vue";
import { onMounted, reactive, ref, toRefs, onBeforeUnmount, computed } from "vue";
import useView from "@/hooks/useView";
import { mediaServerRegister, mediaListInfo } from "@/utils/media";
import AddOrUpdate from "./tcamerainfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globaLang";
import { Search, VideoCamera, VideoCameraFilled, Warning } from "@element-plus/icons-vue";
import app from "@/constants/app";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox, Action } from "element-plus";
import router from "@/router";
import {
  getCameraGrouping,
  checkCameraConnectivity,
  checkCameraStreamStatus,
  checkSingleCameraStatus,
  checkAllCameraStatus,
  getCameraStatusStatistics
} from "@/utils/api";

const dialogTableVisible = ref(false);
const gridData = ref();
const getDetail = (id: number) => {
  return state.dataList?.find((item) => item.id === id);
};
const details = (id: number) => {
  const detail = getDetail(id);
  gridData.value = detail ? [detail] : [];
  dialogTableVisible.value = true;
};

const id = ref("");
const name = ref("");
const deptName = ref<any>("");
const isShow = ref(false);
const { $t } = globalLanguage();
const vInput = ref("");
const view = reactive({
  getDataListURL: "/edge/tcamerainfo/page",
  getDataListIsPage: true,
  exportURL: "/edge/tcamerainfo/export",
  deleteURL: "/edge/tcamerainfo",
  deleteIsBatch: true,
  dataForm: {
    id: "",
    name: "",
    tenantId: "",
    gId: "",
    gName: "",
    cameraStatus: ""
  }
});

const toDeployHandle = () => {
  if (state.dataListSelections?.length === 0) {
    ElMessage.warning("请选择需要部署的摄像头");
    return;
  }
  const arr = state.dataListSelections?.map((item) => item.streamUrl);
  router.push({ path: "/edge/tcontroladdinfo", state: { urls: arr } });
};

baseService.get("/sys/tenant/list").then((res) => {
  deptName.value = res.data;
  if (deptName.value.length === 1) {
    isShow.value = true;
  }
});

interface DataItem {
  streamUrl: string;
  isStreaming: boolean;
  id: string;
  ipAddr: string;
  name: string;
  onlineStatus?: number; // 0-离线 1-在线 2-检测中
  lastCheckTime?: number;
}
interface ArrList {
  flv_http: string;
}
let intervalId: number | undefined;
let cameraStatusCheckInterval: number | undefined;

const fetchServerData = async () => {
  try {
    if (state.dataList) {
      for (const item of state.dataList) {
        if (await mediaListInfo(item.id)) {
          item.isStreaming = true;
        }
      }
    }
  } catch (error) {
    console.error("Error fetching server data:", error);
  }
};

// 检测单个摄像头状态（使用后端API）
const checkCameraStatusById = async (camera: any): Promise<void> => {
  try {
    const response = await checkSingleCameraStatus(camera.id);
    if (response.data && response.data.status !== undefined) {
      camera.onlineStatus = response.data.status;
      camera.lastCheckTime = Date.now();
    }
  } catch (error) {
    console.error(`检测摄像头 ${camera.name} 状态失败:`, error);
    camera.onlineStatus = 0; // 检测失败标记为离线
    camera.lastCheckTime = Date.now();
  }
};

// 批量检测所有摄像头状态
const checkAllCamerasStatus = async () => {
  if (!state.dataList || state.dataList.length === 0) {
    return;
  }

  try {
    // 先标记所有摄像头为检测中
    state.dataList.forEach((camera: any) => {
      camera.onlineStatus = 2; // 检测中
    });

    // 调用后端批量检测API
    await checkAllCameraStatus();

    // 等待一段时间后重新获取数据以获取最新状态
    setTimeout(async () => {
      await state.getDataList();
    }, 3000);

  } catch (error) {
    console.error("批量检测摄像头状态失败:", error);
    ElMessage.error("批量检测摄像头状态失败");
  }
};

// 获取摄像头状态显示文本
const getCameraStatusText = (camera: any): string => {
  if (camera.onlineStatus === undefined || camera.onlineStatus === null) {
    return "未检测";
  }
  switch (camera.onlineStatus) {
    case 1:
      return "在线";
    case 0:
      return "离线";
    case 2:
      return "检测中";
    default:
      return "未知";
  }
};

// 获取摄像头状态标签类型
const getCameraStatusType = (camera: any): string => {
  if (camera.onlineStatus === undefined || camera.onlineStatus === null) {
    return "info";
  }
  switch (camera.onlineStatus) {
    case 1:
      return "success";
    case 0:
      return "danger";
    case 2:
      return "warning";
    default:
      return "info";
  }
};

// 获取摄像头状态图标
const getCameraStatusIcon = (camera: any) => {
  if (camera.onlineStatus === undefined || camera.onlineStatus === null) {
    return VideoCamera;
  }
  switch (camera.onlineStatus) {
    case 1:
      return VideoCameraFilled;
    case 0:
      return Warning;
    case 2:
      return VideoCamera;
    default:
      return VideoCamera;
  }
};

// 格式化最后检测时间
const formatLastCheckTime = (timestamp: number): string => {
  if (!timestamp) return "";

  const now = Date.now();
  const diff = now - timestamp;
  const minutes = Math.floor(diff / (1000 * 60));
  const seconds = Math.floor(diff / 1000);

  if (minutes > 0) {
    return `${minutes}分钟前`;
  } else if (seconds > 0) {
    return `${seconds}秒前`;
  } else {
    return "刚刚";
  }
};

function processString(input: string): string {
  const firstDigitIndex = input.search(/\d/);

  if (firstDigitIndex === -1) {
    return "";
  }

  let result = input.substring(firstDigitIndex);

  const lastColonIndex = result.lastIndexOf(":");

  if (lastColonIndex !== -1) {
    result = result.substring(0, lastColonIndex);
  }

  return result;
}

const state = reactive({ ...useView(view), ...toRefs(view) });
state.dataList = state.dataList?.map((item) => ({
  ...item,
  isStreaming: false
}));

const getTrainName = (videoInput: string) => {
  baseService.get("/edge/predictdeploy/page", { limit: 10000 }).then((res) => {
    const arr = res.data.list.filter((item: { videoInput: string; deployStatus: number }) => item.videoInput == videoInput && item.deployStatus === 3);
    arr.forEach((item: { trainName: string }) => {
      vInput.value += item.trainName + "<br>";
    });
    ElMessageBox.alert(vInput.value === "" ? "未运行算法" : `<div style="background-color: #00000013; padding: 5px; border-radius: 3px; margin-right: 10px;">${vInput.value}</div><br>`, "已运行算法", {
      dangerouslyUseHTMLString: true,
      confirmButtonText: "取消",
      callback: (action: Action) => {
        vInput.value = "";
      }
    });
  });
};

const addOrUpdateRef = ref();
const devicePlayerRef = ref();
const pushArrList = ref<ArrList>({
  flv_http: ""
});

const getByName = () => {
  state.limit = 2;
};

const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const copyStreamUrl = (id?: number) => {
  addOrUpdateRef.value.init(id, 1);
};

const handleClickPushInfo = (row?: DataItem) => {
  const api = processString("http://192.168.1.66:32007");
  const http_port = 32007;
  const https_port = 30237;
  const ws_port = 32007;
  const wss_port = 30237;
  const rtmp_port = 30231;
  const rtsp_port = 30234;
  const list = {
    flv: "/live/" + row?.id + ".live.flv",
    fmp4: "/live/" + row?.id + ".live.mp4",
    hls: "/live/" + row?.id + "/hls.m3u8"
  };
  pushArrList.value = {
    flv_http: "http://" + api + ":" + http_port + list.flv
  };
};

const openPlayer = (streamUrl?: string, channelId?: string) => {
  mediaServerRegister(streamUrl, channelId);
  console.log(devicePlayer);
  devicePlayerRef.value.openDialog("streamPlay", {
    streamInfo: {
      deviceID: "/live",
      channelId: "/" + channelId
    },
    hasAudio: true
  });
};

const startStreaming = (row: DataItem) => {
  console.log(row);
  mediaServerRegister(row.streamUrl, row.id)
    .then(() => {
      row.isStreaming = true;
      ElMessage.success("推流成功");
    })
    .catch((error) => {
      ElMessage.error("推流失败:" + error.message);
    });
};

const stopStreaming = (row: DataItem) => {
  console.log("关闭推流");
  try {
    baseService
      .post("/index/api/close_streams", {
        secret: app.media_secret,
        force: true
      })
      .then((res) => {
        ElMessage.success("关闭推流成功");
        row.isStreaming = false;
      });
  } catch (error) {
    ElMessage.error("关闭推流失败");
  }
};

interface CameraGroupItem {
  id: string;
  gName: string;
}
const searchGroupList = ref<CameraGroupItem[]>([]);
const statusCheckLoading = ref(false);
const statusStatistics = ref({
  total: 0,
  online: 0,
  offline: 0,
  checking: 0,
  uncheck: 0,
  onlineRate: 0
});

// 统计各种状态的摄像头数量
const onlineCameraCount = computed(() => {
  return statusStatistics.value.online;
});

const offlineCameraCount = computed(() => {
  return statusStatistics.value.offline;
});

const checkingCameraCount = computed(() => {
  return statusStatistics.value.checking;
});

const totalCameraCount = computed(() => {
  return statusStatistics.value.total;
});

// 过滤后的摄像头列表
const filteredDataList = computed(() => {
  if (!state.dataList) return [];

  let filtered = [...state.dataList];

  // 根据状态过滤
  if (state.dataForm.cameraStatus) {
    filtered = filtered.filter((camera: any) => {
      if (state.dataForm.cameraStatus === 'online') {
        return camera.onlineStatus === 1;
      } else if (state.dataForm.cameraStatus === 'offline') {
        return camera.onlineStatus === 0;
      }
      return true;
    });
  }

  return filtered;
});

// 手动刷新摄像头状态
const refreshCameraStatus = async () => {
  statusCheckLoading.value = true;
  try {
    await checkAllCamerasStatus();
    ElMessage.success("摄像头状态检测已启动，请稍后查看结果");

    // 获取最新的状态统计
    await loadStatusStatistics();
  } catch (error) {
    console.error("刷新摄像头状态失败:", error);
    ElMessage.error("刷新摄像头状态失败");
  } finally {
    statusCheckLoading.value = false;
  }
};

// 获取状态统计
const loadStatusStatistics = async () => {
  try {
    const response = await getCameraStatusStatistics();
    if (response.data) {
      statusStatistics.value = response.data;
    }
  } catch (error) {
    console.error("获取状态统计失败:", error);
  }
};

const loadSearchGroups = async () => {
  try {
    const response = await getCameraGrouping();
    let actualData: CameraGroupItem[] = [];
    if (response && typeof response.data !== 'undefined' && typeof (response as any).code === 'undefined') {
        if (response.data && response.data.code === 0 && Array.isArray(response.data.data)) {
            actualData = response.data.data;
        } else {
            console.error("加载搜索用分组列表失败 (AxiosResponse.data 格式错误):", response.data);
        }
    } else if (response && typeof (response as any).code === 'number') {
        if ((response as any).code === 0 && Array.isArray((response as any).data)) {
            actualData = (response as any).data;
        } else {
            console.error("加载搜索用分组列表失败 (直接响应格式错误):", response);
        }
    } else if (Array.isArray(response)){
        actualData = response;
    } else {
        console.error("加载搜索用分组列表失败: 未知响应格式", response);
    }
    searchGroupList.value = actualData;
  } catch (error) {
    console.error("加载搜索用摄像头分组列表时出错:", error);
    ElMessage.error("加载搜索用摄像头分组列表失败。");
    searchGroupList.value = [];
  }
};

onMounted(() => {
  fetchServerData();
  intervalId = window.setInterval(fetchServerData, 2000);
  loadSearchGroups();

  // 加载状态统计
  loadStatusStatistics();

  // 每30秒更新一次状态统计
  cameraStatusCheckInterval = window.setInterval(loadStatusStatistics, 30000);
});

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId);
  }
  if (cameraStatusCheckInterval) {
    clearInterval(cameraStatusCheckInterval);
  }
});
</script>

<style scoped>
table {
  width: 100%;
  max-width: 1200px;
  border-collapse: collapse;
  table-layout: fixed;
}
th,
td {
  padding: 10px;
  overflow: auto;
  white-space: nowrap;
}
</style>
