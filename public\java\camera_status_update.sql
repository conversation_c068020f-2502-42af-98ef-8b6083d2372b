-- 摄像头状态检测功能数据库表结构更新SQL

-- 为t_camera_info表添加状态相关字段
ALTER TABLE t_camera_info 
ADD COLUMN online_status INT(1) DEFAULT NULL COMMENT '摄像头在线状态 0-离线 1-在线 2-检测中',
ADD COLUMN last_check_time DATETIME DEFAULT NULL COMMENT '最后检测时间',
ADD COLUMN camera_type INT(1) DEFAULT 2 COMMENT '摄像头类型 1-海康导入 2-地址访问';

-- 创建索引以提高查询性能
CREATE INDEX idx_online_status ON t_camera_info(online_status);
CREATE INDEX idx_camera_type ON t_camera_info(camera_type);
CREATE INDEX idx_last_check_time ON t_camera_info(last_check_time);

-- 更新现有数据，将海康威视制造商的摄像头标记为海康导入类型
UPDATE t_camera_info 
SET camera_type = 1 
WHERE manufacturer = '海康威视' OR manufacturer LIKE '%海康%';

-- 将其他摄像头标记为地址访问类型
UPDATE t_camera_info 
SET camera_type = 2 
WHERE camera_type IS NULL;

-- 初始化所有摄像头状态为检测中
UPDATE t_camera_info 
SET online_status = 2 
WHERE online_status IS NULL;
