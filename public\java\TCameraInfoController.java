package top.mybi.modules.edge.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.HtmlUtils;
import springfox.documentation.annotations.ApiIgnore;
import top.mybi.common.annotation.LogOperation;
import top.mybi.common.constant.Constant;
import top.mybi.common.page.PageData;
import top.mybi.common.utils.ExcelUtils;
import top.mybi.common.utils.Result;
import top.mybi.common.validator.AssertUtils;
import top.mybi.common.validator.ValidatorUtils;
import top.mybi.common.validator.group.AddGroup;
import top.mybi.common.validator.group.DefaultGroup;
import top.mybi.common.validator.group.UpdateGroup;
import top.mybi.modules.edge.dao.TuuidCameraDao;
import top.mybi.modules.edge.dto.CameraGroupUpdateDTO;
import top.mybi.modules.edge.dto.TCameraInfoDTO;
import top.mybi.modules.edge.entity.TuuidCameraEntity;
import top.mybi.modules.edge.excel.TCameraInfoExcel;
import top.mybi.modules.edge.service.TCameraInfoService;
import top.mybi.modules.edge.service.CameraStatusService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 摄像头信息表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-07-27
 */
@RestController
@RequestMapping("edge/tcamerainfo")
@Api(tags = "摄像头信息表")
public class TCameraInfoController {
    @Autowired
    private TCameraInfoService tCameraInfoService;

    @Autowired
    private TuuidCameraDao tuuidCameraDao;

    @Autowired
    private CameraStatusService cameraStatusService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "tenantId", value = "租户ID", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "gId", value = "组ID", paramType = "query", dataType = "int")
    })
    //@RequiresPermissions("edge:tcamerainfo:page")
    public Result<PageData<TCameraInfoDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        //如果当前用户不是管理员，则只能查询当前租户的记录
//        if (SecurityUser.getUser().getSuperAdmin() != 1) {
//            params.put("tenantId", SecurityUser.getTenantId().toString());
//        }

        PageData<TCameraInfoDTO> page = tCameraInfoService.page(params);

        return new Result<PageData<TCameraInfoDTO>>().ok(page);
    }

    @GetMapping("getNameList")
    @ApiOperation("根据名称查询")
    public Result<List<TCameraInfoDTO>> getNameList(@ApiIgnore @RequestParam String name, String id) {
        List<TCameraInfoDTO> nameList = tCameraInfoService.getNameList(name, id);

        return new Result<List<TCameraInfoDTO>>().ok(nameList);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    //@RequiresPermissions("edge:tcamerainfo:info")
    public Result<TCameraInfoDTO> get(@PathVariable("id") Long id) {
        TCameraInfoDTO data = tCameraInfoService.get(id);

        return new Result<TCameraInfoDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    //@RequiresPermissions("edge:tcamerainfo:save")
    public Result save(@RequestBody TCameraInfoDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        String inputUrl = HtmlUtils.htmlUnescape(dto.getStreamUrl());
        dto.setStreamUrl(inputUrl);

        //如果没有tenant_id，证明不是管理员账号，
        // 新增就给默认新增当前操作用户的租户公司id
//        if (dto.getTenantId() == null) {
//            dto.setTenantId(SecurityUser.getTenantId());
//        }
        tCameraInfoService.save(dto);

        return new Result();
    }

    @GetMapping("/AllInfoList")
    @ApiOperation("摄像头全部信息列表")
    public Result<List<TCameraInfoDTO>> AllInfoList() {
        List<TCameraInfoDTO> list = tCameraInfoService.list();
        return new Result<List<TCameraInfoDTO>>().ok(list);
    }


    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    //@RequiresPermissions("edge:tcamerainfo:update")
    public Result update(@RequestBody TCameraInfoDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        String inputUrl = HtmlUtils.htmlUnescape(dto.getStreamUrl());
        dto.setStreamUrl(inputUrl);
        tCameraInfoService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    //@RequiresPermissions("edge:tcamerainfo:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");
        try {
            //根据ids查询摄像头uuid
            List<String> uuidList = tCameraInfoService.getUuidList(ids);
            //根据uuidlist删除tuuid_camera表的记录
            tuuidCameraDao.delete(new QueryWrapper<TuuidCameraEntity>().in("uuid", uuidList));
        } catch (Exception e) {
            e.printStackTrace();
        }
        tCameraInfoService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    //@RequiresPermissions("edge:tcamerainfo:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TCameraInfoDTO> list = tCameraInfoService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, TCameraInfoExcel.class);
    }

    @GetMapping("hikvisionSync")
    @ApiOperation("从海康平台导入")
    @LogOperation("从海康平台导入")
    public void hikvisionSync(@RequestParam String host, @RequestParam String appKey, @RequestParam String appSecret) throws Exception {
        tCameraInfoService.hikvisionSync(host, appKey, appSecret);
    }

    // 更新摄像头的组信息
    @PutMapping("updateGroupInfo")
    @ApiOperation("更新摄像头的组信息")
    @LogOperation("更新摄像头的组信息")
    public Result updateGroupInfo(@RequestBody List<CameraGroupUpdateDTO> dtos) {
        //效验数据
        ValidatorUtils.validateEntity(dtos, UpdateGroup.class, DefaultGroup.class);

        tCameraInfoService.updateGroupInfo(dtos);
        return new Result();
    }

    @GetMapping("getCameraInfoByGroupId/{id}")
    @ApiOperation("根据分组ID获取摄像头信息")
    @LogOperation("根据分组ID获取摄像头信息")
    public Result<List<TCameraInfoDTO>> getCameraInfoByGroupId(@PathVariable("id") Long id) {
        List<TCameraInfoDTO> data = tCameraInfoService.getCameraInfoByGroupId(id);

        return new Result<List<TCameraInfoDTO>>().ok(data);
    }

    @PostMapping("checkConnectivity")
    @ApiOperation("检测摄像头IP连通性")
    @LogOperation("检测摄像头IP连通性")
    public Result<Map<String, Object>> checkConnectivity(@RequestBody Map<String, String> params) {
        String ipAddr = params.get("ipAddr");
        boolean isConnected = cameraStatusService.checkCameraConnectivity(ipAddr);

        Map<String, Object> result = new HashMap<>();
        result.put("success", isConnected);
        result.put("ipAddr", ipAddr);
        result.put("message", isConnected ? "连接成功" : "连接失败");

        return new Result<Map<String, Object>>().ok(result);
    }

    @PostMapping("checkStreamStatus")
    @ApiOperation("检测摄像头流状态")
    @LogOperation("检测摄像头流状态")
    public Result<Map<String, Object>> checkStreamStatus(@RequestBody Map<String, String> params) {
        String streamUrl = params.get("streamUrl");
        boolean isAvailable = cameraStatusService.checkStreamAvailability(streamUrl);

        Map<String, Object> result = new HashMap<>();
        result.put("success", isAvailable);
        result.put("streamUrl", streamUrl);
        result.put("message", isAvailable ? "流可用" : "流不可用");

        return new Result<Map<String, Object>>().ok(result);
    }

    @PostMapping("checkSingleStatus/{id}")
    @ApiOperation("检测单个摄像头状态")
    @LogOperation("检测单个摄像头状态")
    public Result<Map<String, Object>> checkSingleStatus(@PathVariable("id") Long id) {
        TCameraInfoDTO cameraInfo = tCameraInfoService.get(id);
        if (cameraInfo == null) {
            return new Result<Map<String, Object>>().error("摄像头不存在");
        }

        Integer status = cameraStatusService.checkSingleCameraStatus(cameraInfo);

        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("name", cameraInfo.getName());
        result.put("status", status);
        result.put("statusText", status == 1 ? "在线" : status == 0 ? "离线" : "检测中");

        return new Result<Map<String, Object>>().ok(result);
    }

    @PostMapping("checkAllStatus")
    @ApiOperation("检测所有摄像头状态")
    @LogOperation("检测所有摄像头状态")
    public Result<String> checkAllStatus() {
        try {
            cameraStatusService.checkAllCameraStatus();
            return new Result<String>().ok("状态检测任务已启动");
        } catch (Exception e) {
            return new Result<String>().error("状态检测任务启动失败: " + e.getMessage());
        }
    }

    @GetMapping("getStatusStatistics")
    @ApiOperation("获取摄像头状态统计")
    @LogOperation("获取摄像头状态统计")
    public Result<Map<String, Object>> getStatusStatistics() {
        Map<String, Object> statistics = tCameraInfoService.getStatusStatistics();
        return new Result<Map<String, Object>>().ok(statistics);
    }

}
