package top.mybi.modules.edge.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.HtmlUtils;
import springfox.documentation.annotations.ApiIgnore;
import top.mybi.common.annotation.LogOperation;
import top.mybi.common.constant.Constant;
import top.mybi.common.page.PageData;
import top.mybi.common.utils.ExcelUtils;
import top.mybi.common.utils.Result;
import top.mybi.common.validator.AssertUtils;
import top.mybi.common.validator.ValidatorUtils;
import top.mybi.common.validator.group.AddGroup;
import top.mybi.common.validator.group.DefaultGroup;
import top.mybi.common.validator.group.UpdateGroup;
import top.mybi.modules.edge.dao.TuuidCameraDao;
import top.mybi.modules.edge.dto.CameraGroupUpdateDTO;
import top.mybi.modules.edge.dto.TCameraInfoDTO;
import top.mybi.modules.edge.entity.TuuidCameraEntity;
import top.mybi.modules.edge.excel.TCameraInfoExcel;
import top.mybi.modules.edge.service.TCameraInfoService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 摄像头信息表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2023-07-27
 */
@RestController
@RequestMapping("edge/tcamerainfo")
@Api(tags = "摄像头信息表")
public class TCameraInfoController {
    @Autowired
    private TCameraInfoService tCameraInfoService;

    @Autowired
    private TuuidCameraDao tuuidCameraDao;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "tenantId", value = "租户ID", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "gId", value = "组ID", paramType = "query", dataType = "int")
    })
    //@RequiresPermissions("edge:tcamerainfo:page")
    public Result<PageData<TCameraInfoDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        //如果当前用户不是管理员，则只能查询当前租户的记录
//        if (SecurityUser.getUser().getSuperAdmin() != 1) {
//            params.put("tenantId", SecurityUser.getTenantId().toString());
//        }

        PageData<TCameraInfoDTO> page = tCameraInfoService.page(params);

        return new Result<PageData<TCameraInfoDTO>>().ok(page);
    }

    @GetMapping("getNameList")
    @ApiOperation("根据名称查询")
    public Result<List<TCameraInfoDTO>> getNameList(@ApiIgnore @RequestParam String name, String id) {
        List<TCameraInfoDTO> nameList = tCameraInfoService.getNameList(name, id);

        return new Result<List<TCameraInfoDTO>>().ok(nameList);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    //@RequiresPermissions("edge:tcamerainfo:info")
    public Result<TCameraInfoDTO> get(@PathVariable("id") Long id) {
        TCameraInfoDTO data = tCameraInfoService.get(id);

        return new Result<TCameraInfoDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    //@RequiresPermissions("edge:tcamerainfo:save")
    public Result save(@RequestBody TCameraInfoDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        String inputUrl = HtmlUtils.htmlUnescape(dto.getStreamUrl());
        dto.setStreamUrl(inputUrl);

        //如果没有tenant_id，证明不是管理员账号，
        // 新增就给默认新增当前操作用户的租户公司id
//        if (dto.getTenantId() == null) {
//            dto.setTenantId(SecurityUser.getTenantId());
//        }
        tCameraInfoService.save(dto);

        return new Result();
    }

    @GetMapping("/AllInfoList")
    @ApiOperation("摄像头全部信息列表")
    public Result<List<TCameraInfoDTO>> AllInfoList() {
        List<TCameraInfoDTO> list = tCameraInfoService.list();
        return new Result<List<TCameraInfoDTO>>().ok(list);
    }


    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    //@RequiresPermissions("edge:tcamerainfo:update")
    public Result update(@RequestBody TCameraInfoDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        String inputUrl = HtmlUtils.htmlUnescape(dto.getStreamUrl());
        dto.setStreamUrl(inputUrl);
        tCameraInfoService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    //@RequiresPermissions("edge:tcamerainfo:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");
        try {
            //根据ids查询摄像头uuid
            List<String> uuidList = tCameraInfoService.getUuidList(ids);
            //根据uuidlist删除tuuid_camera表的记录
            tuuidCameraDao.delete(new QueryWrapper<TuuidCameraEntity>().in("uuid", uuidList));
        } catch (Exception e) {
            e.printStackTrace();
        }
        tCameraInfoService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    //@RequiresPermissions("edge:tcamerainfo:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TCameraInfoDTO> list = tCameraInfoService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, list, TCameraInfoExcel.class);
    }

    @GetMapping("hikvisionSync")
    @ApiOperation("从海康平台导入")
    @LogOperation("从海康平台导入")
    public void hikvisionSync(@RequestParam String host, @RequestParam String appKey, @RequestParam String appSecret) throws Exception {
        tCameraInfoService.hikvisionSync(host, appKey, appSecret);
    }

    // 更新摄像头的组信息
    @PutMapping("updateGroupInfo")
    @ApiOperation("更新摄像头的组信息")
    @LogOperation("更新摄像头的组信息")
    public Result updateGroupInfo(@RequestBody List<CameraGroupUpdateDTO> dtos) {
        //效验数据
        ValidatorUtils.validateEntity(dtos, UpdateGroup.class, DefaultGroup.class);

        tCameraInfoService.updateGroupInfo(dtos);
        return new Result();
    }

    @GetMapping("getCameraInfoByGroupId/{id}")
    @ApiOperation("根据分组ID获取摄像头信息")
    @LogOperation("根据分组ID获取摄像头信息")
    public Result<List<TCameraInfoDTO>> getCameraInfoByGroupId(@PathVariable("id") Long id) {
        List<TCameraInfoDTO> data = tCameraInfoService.getCameraInfoByGroupId(id);

        return new Result<List<TCameraInfoDTO>>().ok(data);
    }

}
