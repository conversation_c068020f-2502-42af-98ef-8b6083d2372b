package top.mybi.modules.edge.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.mybi.modules.edge.dao.TCameraInfoDao;
import top.mybi.modules.edge.dto.TCameraInfoDTO;
import top.mybi.modules.edge.entity.TCameraInfoEntity;
import top.mybi.modules.edge.service.CameraStatusService;
import top.mybi.modules.edge.service.TCameraInfoService;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 摄像头状态检测服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class CameraStatusServiceImpl implements CameraStatusService {
    
    @Autowired
    private TCameraInfoService tCameraInfoService;
    
    @Autowired
    private TCameraInfoDao tCameraInfoDao;
    
    // 线程池用于并发检测
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    // 连接超时时间（毫秒）
    private static final int CONNECTION_TIMEOUT = 5000;
    
    // Ping超时时间（毫秒）
    private static final int PING_TIMEOUT = 3000;
    
    @Override
    public Integer checkSingleCameraStatus(TCameraInfoDTO cameraInfo) {
        try {
            log.info("开始检测摄像头状态: {}", cameraInfo.getName());
            
            // 根据摄像头类型选择不同的检测方法
            Integer status;
            if (cameraInfo.getCameraType() != null && cameraInfo.getCameraType() == 1) {
                // 海康导入的摄像头
                status = checkHikvisionCameraStatus(cameraInfo);
            } else {
                // 地址访问的摄像头
                status = checkRtspCameraStatus(cameraInfo);
            }
            
            // 更新数据库中的状态和检测时间
            updateCameraStatus(cameraInfo.getId(), status);
            
            log.info("摄像头 {} 状态检测完成，状态: {}", cameraInfo.getName(), 
                    status == 1 ? "在线" : status == 0 ? "离线" : "检测中");
            
            return status;
        } catch (Exception e) {
            log.error("检测摄像头状态失败: {}", cameraInfo.getName(), e);
            // 检测失败时标记为离线
            updateCameraStatus(cameraInfo.getId(), 0);
            return 0;
        }
    }
    
    @Override
    public void batchCheckCameraStatus(List<TCameraInfoDTO> cameraList) {
        log.info("开始批量检测摄像头状态，数量: {}", cameraList.size());
        
        // 使用CompletableFuture并发检测
        CompletableFuture<Void>[] futures = cameraList.stream()
                .map(camera -> CompletableFuture.runAsync(() -> {
                    checkSingleCameraStatus(camera);
                }, executorService))
                .toArray(CompletableFuture[]::new);
        
        // 等待所有检测完成
        CompletableFuture.allOf(futures).join();
        
        log.info("批量检测摄像头状态完成");
    }
    
    @Override
    public void checkAllCameraStatus() {
        try {
            log.info("开始检测所有摄像头状态");
            
            // 获取所有摄像头列表
            List<TCameraInfoDTO> allCameras = tCameraInfoService.list();
            
            if (allCameras == null || allCameras.isEmpty()) {
                log.info("没有找到摄像头，跳过状态检测");
                return;
            }
            
            // 批量检测
            batchCheckCameraStatus(allCameras);
            
            log.info("所有摄像头状态检测完成，总数: {}", allCameras.size());
        } catch (Exception e) {
            log.error("检测所有摄像头状态失败", e);
        }
    }
    
    @Override
    public Integer checkHikvisionCameraStatus(TCameraInfoDTO cameraInfo) {
        try {
            // 对于海康摄像头，可以通过API检查设备状态
            if (StringUtils.isNotBlank(cameraInfo.getCamId())) {
                // 这里可以调用海康的设备状态查询API
                // 暂时使用IP连通性检测
                if (StringUtils.isNotBlank(cameraInfo.getIpAddr())) {
                    boolean isConnected = checkCameraConnectivity(cameraInfo.getIpAddr());
                    return isConnected ? 1 : 0;
                }
            }
            
            // 如果没有IP地址，尝试检测流地址
            if (StringUtils.isNotBlank(cameraInfo.getStreamUrl())) {
                boolean isStreamAvailable = checkStreamAvailability(cameraInfo.getStreamUrl());
                return isStreamAvailable ? 1 : 0;
            }
            
            return 0; // 无法检测时标记为离线
        } catch (Exception e) {
            log.error("检测海康摄像头状态失败: {}", cameraInfo.getName(), e);
            return 0;
        }
    }
    
    @Override
    public Integer checkRtspCameraStatus(TCameraInfoDTO cameraInfo) {
        try {
            // 优先检测IP连通性
            if (StringUtils.isNotBlank(cameraInfo.getIpAddr())) {
                boolean isConnected = checkCameraConnectivity(cameraInfo.getIpAddr());
                if (!isConnected) {
                    return 0; // IP不通，直接返回离线
                }
            }
            
            // 检测RTSP流是否可用
            if (StringUtils.isNotBlank(cameraInfo.getStreamUrl())) {
                boolean isStreamAvailable = checkStreamAvailability(cameraInfo.getStreamUrl());
                return isStreamAvailable ? 1 : 0;
            }
            
            return 0; // 无法检测时标记为离线
        } catch (Exception e) {
            log.error("检测RTSP摄像头状态失败: {}", cameraInfo.getName(), e);
            return 0;
        }
    }
    
    @Override
    public boolean checkCameraConnectivity(String ipAddress) {
        try {
            if (StringUtils.isBlank(ipAddress)) {
                return false;
            }
            
            // 使用InetAddress.isReachable方法检测连通性
            InetAddress address = InetAddress.getByName(ipAddress);
            boolean isReachable = address.isReachable(PING_TIMEOUT);
            
            if (!isReachable) {
                // 如果ping不通，尝试TCP连接常用端口
                return checkTcpConnection(ipAddress, 80) || 
                       checkTcpConnection(ipAddress, 554) || 
                       checkTcpConnection(ipAddress, 8000);
            }
            
            return true;
        } catch (Exception e) {
            log.warn("检测IP连通性失败: {}", ipAddress, e);
            return false;
        }
    }
    
    @Override
    public boolean checkStreamAvailability(String streamUrl) {
        try {
            if (StringUtils.isBlank(streamUrl)) {
                return false;
            }
            
            // 解析RTSP URL获取主机和端口
            if (streamUrl.startsWith("rtsp://")) {
                String[] parts = streamUrl.replace("rtsp://", "").split("/");
                if (parts.length > 0) {
                    String hostPort = parts[0];
                    String[] hostPortArray = hostPort.split(":");
                    String host = hostPortArray[0];
                    int port = hostPortArray.length > 1 ? Integer.parseInt(hostPortArray[1]) : 554;
                    
                    return checkTcpConnection(host, port);
                }
            }
            
            return false;
        } catch (Exception e) {
            log.warn("检测流可用性失败: {}", streamUrl, e);
            return false;
        }
    }
    
    /**
     * 检测TCP连接
     */
    private boolean checkTcpConnection(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), CONNECTION_TIMEOUT);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 更新摄像头状态
     */
    private void updateCameraStatus(Long cameraId, Integer status) {
        try {
            TCameraInfoEntity entity = new TCameraInfoEntity();
            entity.setId(cameraId);
            entity.setOnlineStatus(status);
            entity.setLastCheckTime(new Date());
            
            tCameraInfoDao.updateById(entity);
        } catch (Exception e) {
            log.error("更新摄像头状态失败, ID: {}, 状态: {}", cameraId, status, e);
        }
    }
}
